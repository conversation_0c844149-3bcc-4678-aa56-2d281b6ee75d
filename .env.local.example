# LOCAL DEVELOPMENT ENVIRONMENT VARIABLES
# Copy this file to .env.local and fill in your actual values

# SECURITY CONFIGURATION
# Generate using: openssl rand -base64 32
NEXTAUTH_SECRET=your-nextauth-secret-here-use-openssl-rand-base64-32

# APP CONFIGURATION
NEXT_PUBLIC_APP_URL=http://localhost:3000
NODE_ENV=development

# SUPABASE CONFIGURATION (Required)
# Get these from your Supabase project settings > API
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key-here

# GEMINI AI CONFIGURATION (Required)
# Get this from Google AI Studio: https://ai.google.dev/
GEMINI_API_KEY=your-gemini-api-key-here

# STRIPE CONFIGURATION (Optional - for payments)
# Get these from your Stripe Dashboard
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
STRIPE_SECRET_KEY=sk_test_your_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
STRIPE_PRICE_ID=price_your_price_id_here
NEXT_PUBLIC_ENABLE_STRIPE=false

# EMAIL CONFIGURATION (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# PEXELS API (Optional - for stock images)
PEXELS_API_KEY=your-pexels-api-key-here
