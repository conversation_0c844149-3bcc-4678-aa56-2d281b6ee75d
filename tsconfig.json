{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": false, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "paths": {"@/*": ["./*"], "@/components/*": ["components/*"], "@/lib/*": ["lib/*"], "@/types/*": ["types/*"], "@/styles/*": ["styles/*"], "@/hooks/*": ["hooks/*"]}, "typeRoots": ["./node_modules/@types", "./types"], "types": ["node", "next"], "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "types/**/*.d.ts", "types/**/*.ts", ".next/types/**/*.ts"], "exclude": ["node_modules", ".next"]}