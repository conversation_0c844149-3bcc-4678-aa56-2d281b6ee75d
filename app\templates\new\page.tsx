import type { Metada<PERSON> } from "next";
import { SiteHeader } from "@/components/site-header";
import { CreateTemplateForm } from "@/components/templates/create-template-form";

// Define metadata for the page
export const metadata: Metadata = {
  title: "New Template | DocMagic",
  description: "Create a new template for your documents",
};

// Define the page component
export default function NewTemplatePage() {
  return (
    <div className="min-h-screen flex flex-col">
      <SiteHeader />
      <main className="flex-1 relative z-10 bg-gradient-to-br from-blue-50 via-background to-purple-50 dark:from-gray-900 dark:via-background dark:to-gray-800 min-h-screen">
        <div className="container mx-auto py-8 sm:py-12 px-4 sm:px-6 lg:px-8">
          {/* Enhanced Header */}
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-2 bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200 px-4 py-2 rounded-full text-sm font-medium mb-4 border border-blue-200 dark:border-blue-700">
              ✨ AI-Powered Template Creator
            </div>
            <h1 className="text-4xl sm:text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400 bg-clip-text text-transparent mb-4">
              Create Your Perfect Template
            </h1>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Build professional documents with our AI-powered template creator.
              Get started in seconds with intelligent suggestions and customizable designs.
            </p>
          </div>
          <CreateTemplateForm />
        </div>
      </main>
    </div>
  );
}
