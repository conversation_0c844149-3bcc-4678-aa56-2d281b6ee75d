@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light mode with F3E9DC base */
    --background: 30 56% 91%; /* #F3E9DC */
    --foreground: 210 11% 15%; /* Professional dark text */
    --card: 30 56% 96%; /* Slightly lighter than background */
    --card-foreground: 210 11% 15%;
    --popover: 30 56% 96%;
    --popover-foreground: 210 11% 15%;
    --primary: 210 11% 15%; /* Professional dark */
    --primary-foreground: 30 56% 91%;
    --secondary: 30 56% 85%; /* Muted cream */
    --secondary-foreground: 210 11% 15%;
    --muted: 30 56% 85%;
    --muted-foreground: 210 8% 45%;
    --accent: 30 56% 80%; /* Soft accent */
    --accent-foreground: 210 11% 15%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 30 56% 91%;
    --border: 30 40% 80%; /* Subtle borders */
    --input: 30 40% 80%;
    --ring: 210 11% 15%;
    --chart-1: 210 70% 50%; /* Professional blues */
    --chart-2: 195 60% 45%; /* Teals */
    --chart-3: 160 50% 40%; /* Greens */
    --chart-4: 45 70% 55%; /* Golds */
    --chart-5: 25 60% 50%; /* Oranges */
    --radius: 0.5rem;
  }
  .dark {
    /* Ultra Dark mode with 131010 base - Maximum Visibility */
    --background: 0 0% 7%; /* #131010 */
    --foreground: 0 0% 100%; /* Pure white text for maximum contrast */
    --card: 0 0% 12%; /* Lighter card background */
    --card-foreground: 0 0% 100%;
    --popover: 0 0% 12%;
    --popover-foreground: 0 0% 100%;
    --primary: 0 0% 100%; /* Pure white primary */
    --primary-foreground: 0 0% 7%;
    --secondary: 0 0% 18%; /* Much lighter secondary */
    --secondary-foreground: 0 0% 100%;
    --muted: 0 0% 18%; /* Lighter muted background */
    --muted-foreground: 0 0% 90%; /* Very bright muted text */
    --accent: 0 0% 25%; /* Lighter accent */
    --accent-foreground: 0 0% 100%;
    --destructive: 0 85% 70%; /* Bright destructive */
    --destructive-foreground: 0 0% 100%;
    --border: 0 0% 30%; /* Much lighter borders */
    --input: 0 0% 25%; /* Lighter input backgrounds */
    --ring: 0 0% 100%;
    --chart-1: 210 95% 80%; /* Ultra bright blues */
    --chart-2: 195 85% 75%; /* Ultra bright teals */
    --chart-3: 160 75% 70%; /* Ultra bright greens */
    --chart-4: 45 95% 85%; /* Ultra bright golds */
    --chart-5: 25 85% 80%; /* Ultra bright oranges */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-poppins), system-ui, sans-serif;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
    font-variation-settings: "opsz" auto;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    scroll-behavior: smooth;
    overflow-x: hidden;
  }

  /* Optimize font loading */
  @font-face {
    font-family: "Poppins";
    font-display: swap;
  }
}
.badge-bg {
  background: linear-gradient(
    to right,
    #4885adda,
    rgb(230, 230, 152)
  ); /* slate hues */
}

/* Professional Color Design System */
@layer components {
  /* Professional Primary Gradients */
  .bolt-gradient {
    background: linear-gradient(
      135deg,
      #2563eb 0%,
      #1d4ed8 25%,
      #1e40af 50%,
      #1e3a8a 75%,
      #312e81 100%
    );
  }

  .bolt-gradient-text {
    background: linear-gradient(
      135deg,
      #2563eb 0%,
      #1d4ed8 25%,
      #1e40af 50%,
      #1e3a8a 75%,
      #312e81 100%
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 200%;
    animation: gradient-shift 6s ease infinite;
  }

  /* Professional Secondary Gradients */
  .sunset-gradient {
    background: linear-gradient(
      135deg,
      #f59e0b 0%,
      #d97706 25%,
      #b45309 50%,
      #92400e 75%,
      #78350f 100%
    );
  }

  .ocean-gradient {
    background: linear-gradient(
      135deg,
      #0891b2 0%,
      #0e7490 25%,
      #155e75 50%,
      #164e63 75%,
      #083344 100%
    );
  }

  .forest-gradient {
    background: linear-gradient(
      135deg,
      #059669 0%,
      #047857 25%,
      #065f46 50%,
      #064e3b 75%,
      #022c22 100%
    );
  }

  .cosmic-gradient {
    background: linear-gradient(
      135deg,
      #7c3aed 0%,
      #6d28d9 25%,
      #5b21b6 50%,
      #4c1d95 75%,
      #3730a3 100%
    );
  }

  /* Professional Glow Effects */
  .bolt-glow {
    box-shadow: 0 0 20px rgba(37, 99, 235, 0.3), 0 0 40px rgba(29, 78, 216, 0.2),
      0 0 60px rgba(30, 64, 175, 0.1);
  }

  .sunset-glow {
    box-shadow: 0 0 20px rgba(245, 158, 11, 0.3),
      0 0 40px rgba(217, 119, 6, 0.2), 0 0 60px rgba(180, 83, 9, 0.1);
  }

  .ocean-glow {
    box-shadow: 0 0 20px rgba(8, 145, 178, 0.3),
      0 0 40px rgba(14, 116, 144, 0.2), 0 0 60px rgba(21, 94, 117, 0.1);
  }

  .glass-effect {
    background: rgba(254, 251, 199, 0.3); /* Light cream with transparency */
    backdrop-filter: blur(12px);
    border: 1px solid rgba(254, 251, 199, 0.4);
  }

  .dark .glass-effect {
    background: rgba(19, 16, 16, 0.9); /* Updated for #131010 */
    backdrop-filter: blur(24px);
    border: 1px solid rgba(255, 255, 255, 0.2); /* Brighter border for contrast */
    box-shadow: 0 8px 40px rgba(0, 0, 0, 0.6); /* Much stronger shadow */
  }

  .mesh-gradient {
    background: radial-gradient(
        at 40% 20%,
        hsla(220, 70%, 60%, 0.4) 0px,
        transparent 50%
      ),
      radial-gradient(at 80% 0%, hsla(195, 60%, 50%, 0.3) 0px, transparent 50%),
      radial-gradient(at 0% 50%, hsla(45, 70%, 55%, 0.3) 0px, transparent 50%),
      radial-gradient(at 80% 50%, hsla(160, 50%, 45%, 0.3) 0px, transparent 50%),
      radial-gradient(at 0% 100%, hsla(280, 60%, 55%, 0.3) 0px, transparent 50%),
      radial-gradient(
        at 80% 100%,
        hsla(210, 70%, 50%, 0.4) 0px,
        transparent 50%
      );
    animation: mesh-float 12s ease-in-out infinite;
  }

  .mesh-gradient-alt {
    background: radial-gradient(
        at 20% 30%,
        hsla(195, 60%, 50%, 0.3) 0px,
        transparent 50%
      ),
      radial-gradient(at 70% 10%, hsla(45, 70%, 55%, 0.3) 0px, transparent 50%),
      radial-gradient(at 10% 70%, hsla(160, 50%, 45%, 0.3) 0px, transparent 50%),
      radial-gradient(at 90% 60%, hsla(280, 60%, 55%, 0.3) 0px, transparent 50%),
      radial-gradient(at 30% 90%, hsla(220, 70%, 60%, 0.3) 0px, transparent 50%);
    animation: mesh-float 15s ease-in-out infinite reverse;
  }

  .floating-orb {
    position: absolute;
    border-radius: 50%;
    filter: blur(1px);
    animation: float 6s ease-in-out infinite;
  }

  .floating-orb:nth-child(1) {
    animation-delay: 0s;
  }
  .floating-orb:nth-child(2) {
    animation-delay: 2s;
  }
  .floating-orb:nth-child(3) {
    animation-delay: 4s;
  }

  /* Enhanced animations */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  /* Mobile optimizations */
  @media (max-width: 768px) {
    .floating-orb {
      width: 80px !important;
      height: 80px !important;
      opacity: 0.08 !important;
    }

    .bolt-glow {
      box-shadow: 0 0 10px rgba(255, 215, 0, 0.2),
        0 0 20px rgba(74, 144, 226, 0.1);
    }

    .glass-effect {
      backdrop-filter: blur(8px);
    }

    .mesh-gradient {
      opacity: 0.1 !important;
    }
  }

  /* Enhanced hover effects */
  .hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }

  /* Professional Gradient Borders */
  .gradient-border {
    position: relative;
    background: linear-gradient(135deg, #2563eb, #f59e0b, #059669, #7c3aed);
    padding: 2px;
    border-radius: 12px;
    background-size: 200% 200%;
    animation: gradient-shift 4s ease infinite;
  }

  .gradient-border::before {
    content: "";
    position: absolute;
    inset: 2px;
    background: var(--background);
    border-radius: 10px;
  }

  /* Professional Card Variants */
  .card-coral {
    background: linear-gradient(
      135deg,
      rgba(245, 158, 11, 0.08) 0%,
      rgba(217, 119, 6, 0.04) 100%
    );
    border: 1px solid rgba(245, 158, 11, 0.15);
  }

  .dark .card-coral {
    background: linear-gradient(
      135deg,
      rgba(245, 158, 11, 0.15) 0%,
      rgba(217, 119, 6, 0.08) 100%
    );
    border: 1px solid rgba(245, 158, 11, 0.3);
  }

  .card-mint {
    background: linear-gradient(
      135deg,
      rgba(5, 150, 105, 0.08) 0%,
      rgba(4, 120, 87, 0.04) 100%
    );
    border: 1px solid rgba(5, 150, 105, 0.15);
  }

  .dark .card-mint {
    background: linear-gradient(
      135deg,
      rgba(5, 150, 105, 0.15) 0%,
      rgba(4, 120, 87, 0.08) 100%
    );
    border: 1px solid rgba(5, 150, 105, 0.3);
  }

  .card-sky {
    background: linear-gradient(
      135deg,
      rgba(37, 99, 235, 0.08) 0%,
      rgba(29, 78, 216, 0.04) 100%
    );
    border: 1px solid rgba(37, 99, 235, 0.15);
  }

  .dark .badge-bg {
    background: linear-gradient(
      to right,
      #5c6062,
      #1e40af
    ); /* Violet to deep blue */
  }
  .dark .card-sky {
    background: linear-gradient(
      135deg,
      rgba(37, 99, 235, 0.15) 0%,
      rgba(29, 78, 216, 0.08) 100%
    );
    border: 1px solid rgba(37, 99, 235, 0.3);
  }

  .card-lavender {
    background: linear-gradient(
      135deg,
      rgba(124, 58, 237, 0.08) 0%,
      rgba(109, 40, 217, 0.04) 100%
    );
    border: 1px solid rgba(124, 58, 237, 0.15);
  }

  .dark .card-lavender {
    background: linear-gradient(
      135deg,
      rgba(124, 58, 237, 0.15) 0%,
      rgba(109, 40, 217, 0.08) 100%
    );
    border: 1px solid rgba(124, 58, 237, 0.3);
  }

  /* Professional Interactive States */
  .hover-coral:hover {
    background: linear-gradient(
      135deg,
      rgba(245, 158, 11, 0.12) 0%,
      rgba(217, 119, 6, 0.08) 100%
    );
    border-color: rgba(245, 158, 11, 0.25);
    box-shadow: 0 10px 25px rgba(245, 158, 11, 0.1);
  }

  .dark .hover-coral:hover {
    background: linear-gradient(
      135deg,
      rgba(245, 158, 11, 0.25) 0%,
      rgba(217, 119, 6, 0.15) 100%
    );
    border-color: rgba(245, 158, 11, 0.5);
    box-shadow: 0 10px 25px rgba(245, 158, 11, 0.2);
  }

  .hover-glow-coral:hover {
    box-shadow: 0 0 25px rgba(245, 158, 11, 0.3);
  }

  .hover-mint:hover {
    background: linear-gradient(
      135deg,
      rgba(5, 150, 105, 0.12) 0%,
      rgba(4, 120, 87, 0.08) 100%
    );
    border-color: rgba(5, 150, 105, 0.25);
    box-shadow: 0 10px 25px rgba(5, 150, 105, 0.1);
  }

  .dark .hover-mint:hover {
    background: linear-gradient(
      135deg,
      rgba(5, 150, 105, 0.25) 0%,
      rgba(4, 120, 87, 0.15) 100%
    );
    border-color: rgba(5, 150, 105, 0.5);
    box-shadow: 0 10px 25px rgba(5, 150, 105, 0.2);
  }

  .hover-glow-mint:hover {
    box-shadow: 0 0 25px rgba(5, 150, 105, 0.3);
  }

  .hover-sky:hover {
    background: linear-gradient(
      135deg,
      rgba(37, 99, 235, 0.12) 0%,
      rgba(29, 78, 216, 0.08) 100%
    );
    border-color: rgba(37, 99, 235, 0.25);
    box-shadow: 0 10px 25px rgba(37, 99, 235, 0.1);
  }

  .dark .hover-sky:hover {
    background: linear-gradient(
      135deg,
      rgba(37, 99, 235, 0.25) 0%,
      rgba(29, 78, 216, 0.15) 100%
    );
    border-color: rgba(37, 99, 235, 0.5);
    box-shadow: 0 10px 25px rgba(37, 99, 235, 0.2);
  }

  .hover-glow-sky:hover {
    box-shadow: 0 0 25px rgba(37, 99, 235, 0.3);
  }

  .hover-lavender:hover {
    background: linear-gradient(
      135deg,
      rgba(124, 58, 237, 0.12) 0%,
      rgba(109, 40, 217, 0.08) 100%
    );
    border-color: rgba(124, 58, 237, 0.25);
    box-shadow: 0 10px 25px rgba(124, 58, 237, 0.1);
  }

  .dark .hover-lavender:hover {
    background: linear-gradient(
      135deg,
      rgba(124, 58, 237, 0.25) 0%,
      rgba(109, 40, 217, 0.15) 100%
    );
    border-color: rgba(124, 58, 237, 0.5);
    box-shadow: 0 10px 25px rgba(124, 58, 237, 0.2);
  }

  /* Professional Section Styling */
  .section-header {
    position: relative;
    background: linear-gradient(
      135deg,
      rgba(37, 99, 235, 0.02) 0%,
      rgba(245, 158, 11, 0.02) 100%
    );
    border-bottom: 1px solid hsl(var(--border));
  }

  .dark .section-header {
    background: linear-gradient(
      135deg,
      rgba(37, 99, 235, 0.08) 0%,
      rgba(245, 158, 11, 0.08) 100%
    );
    border-bottom: 1px solid rgba(254, 251, 199, 0.15);
  }

  .professional-card {
    background: hsl(var(--card));
    border: 1px solid hsl(var(--border));
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  }

  .dark .professional-card {
    background: hsl(var(--card));
    border: 1px solid hsl(var(--border));
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  }

  .professional-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
    border-color: rgba(37, 99, 235, 0.3);
  }

  .dark .professional-card:hover {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    border-color: rgba(37, 99, 235, 0.5);
  }

  /* Modern Typography System */
  .professional-heading {
    font-family: var(--font-poppins), system-ui, sans-serif;
    font-weight: 700;
    letter-spacing: -0.04em;
    line-height: 1.1;
    color: hsl(var(--foreground));
    font-feature-settings: "ss01", "ss02";
  }

  .professional-subheading {
    font-family: var(--font-poppins), system-ui, sans-serif;
    font-weight: 600;
    letter-spacing: -0.02em;
    line-height: 1.3;
    color: hsl(var(--muted-foreground));
    margin-bottom: 0.5rem;
  }

  .professional-text {
    font-family: var(--font-poppins), system-ui, sans-serif;
    font-weight: 400;
    color: hsl(var(--muted-foreground));
    line-height: 1.7;
    letter-spacing: -0.01em;
  }

  .modern-display {
    font-family: var(--font-poppins), system-ui, sans-serif;
    font-weight: 800;
    letter-spacing: -0.05em;
    line-height: 1;
    font-feature-settings: "ss01", "ss02", "cv01";
  }

  .modern-title {
    font-family: var(--font-poppins), system-ui, sans-serif;
    font-weight: 700;
    letter-spacing: -0.03em;
    line-height: 1.2;
    font-feature-settings: "ss01";
  }

  .modern-body {
    font-family: var(--font-poppins), system-ui, sans-serif;
    font-weight: 400;
    line-height: 1.6;
    letter-spacing: -0.01em;
  }

  /* Professional Buttons */
  .btn-primary {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
  }

  .btn-primary:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(37, 99, 235, 0.4);
  }

  .btn-secondary {
    background: hsl(var(--secondary));
    color: hsl(var(--secondary-foreground));
    border: 1px solid hsl(var(--border));
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
  }

  .btn-secondary:hover {
    background: hsl(var(--accent));
    border-color: rgba(37, 99, 235, 0.3);
    transform: translateY(-1px);
  }

  .dark .btn-secondary:hover {
    border-color: rgba(37, 99, 235, 0.5);
  }

  /* Professional Navigation */
  .nav-professional {
    background: hsl(var(--background) / 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid hsl(var(--border));
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .dark .nav-professional {
    background: hsl(var(--background) / 0.95);
    border-bottom: 1px solid hsl(var(--border));
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
  }

  /* Professional Footer */
  .footer-professional {
    background: linear-gradient(
      135deg,
      hsl(var(--muted)) 0%,
      hsl(var(--background)) 100%
    );
    border-top: 1px solid hsl(var(--border));
    padding: 3rem 0 2rem;
  }

  .dark .footer-professional {
    background: linear-gradient(
      135deg,
      hsl(var(--muted)) 0%,
      hsl(var(--background)) 100%
    );
    border-top: 1px solid hsl(var(--border));
  }

  /* Enhanced Dark Mode Text Visibility - 0E2148 */
  .dark .professional-text {
    color: hsl(var(--muted-foreground));
    opacity: 1;
  }

  .dark .professional-heading {
    color: hsl(var(--foreground));
    font-weight: 700;
  }

  .dark .professional-subheading {
    color: hsl(var(--muted-foreground));
    opacity: 1;
  }

  /* Improved Dark Mode Borders and Shadows */
  .dark .professional-card {
    border-color: hsl(var(--border));
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.6);
    background: hsl(var(--card));
  }

  .dark .professional-card:hover {
    box-shadow: 0 8px 48px rgba(0, 0, 0, 0.7);
    border-color: rgba(37, 99, 235, 0.7);
  }

  /* Enhanced Dark Mode Navigation */
  .dark .nav-professional {
    background: hsl(var(--background) / 0.95);
    backdrop-filter: blur(28px);
    border-bottom: 1px solid hsl(var(--border));
    box-shadow: 0 1px 12px rgba(0, 0, 0, 0.4);
  }

  /* Dark Mode Section Headers */
  .dark .section-header {
    background: linear-gradient(
      135deg,
      rgba(37, 99, 235, 0.12) 0%,
      rgba(245, 158, 11, 0.12) 100%
    );
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Dark Mode Footer */
  .dark .footer-professional {
    background: linear-gradient(
      135deg,
      hsl(var(--muted)) 0%,
      hsl(var(--background)) 100%
    );
    border-top: 1px solid hsl(var(--border));
  }
}

/* Enhanced keyframe animations - Performance Optimized */
@keyframes float {
  0%,
  100% {
    transform: translate3d(0, 0px, 0) rotate(0deg) scale(1);
  }
  33% {
    transform: translate3d(0, -20px, 0) rotate(120deg) scale(1.05);
  }
  66% {
    transform: translate3d(0, 10px, 0) rotate(240deg) scale(0.95);
  }
}

@keyframes float-gentle {
  0%,
  100% {
    transform: translate3d(0, 0px, 0);
  }
  50% {
    transform: translate3d(0, -10px, 0);
  }
}

@keyframes slide-in-left {
  0% {
    transform: translate3d(-100px, 0, 0);
    opacity: 0;
  }
  100% {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}

@keyframes slide-in-right {
  0% {
    transform: translate3d(100px, 0, 0);
    opacity: 0;
  }
  100% {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}

@keyframes scale-in {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes bounce-in {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes glow-pulse {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(37, 99, 235, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(37, 99, 235, 0.6),
      0 0 60px rgba(245, 158, 11, 0.4);
  }
}

@keyframes text-glow {
  0%,
  100% {
    text-shadow: 0 0 10px rgba(37, 99, 235, 0.3);
  }
  50% {
    text-shadow: 0 0 20px rgba(37, 99, 235, 0.6),
      0 0 30px rgba(245, 158, 11, 0.4);
  }
}

@keyframes gradient-shift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes mesh-float {
  0%,
  100% {
    transform: translateY(0px) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-10px) scale(1.02);
    opacity: 0.8;
  }
}

@keyframes pulse-glow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(37, 99, 235, 0.3),
      0 0 40px rgba(245, 158, 11, 0.2);
  }
  50% {
    box-shadow: 0 0 40px rgba(37, 99, 235, 0.5),
      0 0 60px rgba(245, 158, 11, 0.4), 0 0 80px rgba(5, 150, 105, 0.3);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes subtle-shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@keyframes sparkle {
  0%,
  100% {
    opacity: 0.3;
    transform: scale(0.8) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1.2) rotate(180deg);
  }
}

@keyframes color-dance {
  0% {
    filter: hue-rotate(0deg) saturate(1);
  }
  25% {
    filter: hue-rotate(90deg) saturate(1.2);
  }
  50% {
    filter: hue-rotate(180deg) saturate(1.4);
  }
  75% {
    filter: hue-rotate(270deg) saturate(1.2);
  }
  100% {
    filter: hue-rotate(360deg) saturate(1);
  }
}

@keyframes rainbow-border {
  0% {
    border-color: #2563eb;
  }
  20% {
    border-color: #f59e0b;
  }
  40% {
    border-color: #059669;
  }
  60% {
    border-color: #7c3aed;
  }
  80% {
    border-color: #0891b2;
  }
  100% {
    border-color: #2563eb;
  }
}

@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-down {
  0% {
    opacity: 0;
    transform: translateY(-30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes infinite-scroll {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-50%);
  }
}

.animate-infinite-scroll {
  animation: infinite-scroll 40s linear infinite;
}

.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out forwards;
  opacity: 0;
}

.animate-fade-in-down {
  animation: fade-in-down 0.6s ease-out forwards;
  opacity: 0;
}

/* New enhanced animations for auth pages */
@keyframes enhanced-shimmer {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

@keyframes micro-bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-2px);
  }
}

@keyframes gentle-glow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(245, 158, 11, 0.2);
  }
  50% {
    box-shadow: 0 0 40px rgba(245, 158, 11, 0.4),
      0 0 60px rgba(37, 99, 235, 0.2);
  }
}

@keyframes progress-fill {
  0% {
    width: 0;
  }
  100% {
    width: 100%;
  }
}

@keyframes field-focus-glow {
  0% {
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(245, 158, 11, 0.1);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0);
  }
}

@keyframes button-pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

/* Enhanced animation classes for auth pages */
.animate-enhanced-shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  background-size: 200% 100%;
  animation: enhanced-shimmer 2s infinite;
}

.animate-micro-bounce {
  animation: micro-bounce 0.3s ease-in-out;
}

.animate-gentle-glow {
  animation: gentle-glow 2s ease-in-out infinite;
}

.animate-progress-fill {
  animation: progress-fill 0.8s ease-out forwards;
}

.animate-field-focus-glow {
  animation: field-focus-glow 0.6s ease-out;
}

/* Epic Button Hover Effects */
@keyframes button-hover-scale {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.08);
  }
}

@keyframes button-hover-glow {
  0% {
    box-shadow: 0 0 20px rgba(245, 158, 11, 0.3),
      0 0 40px rgba(37, 99, 235, 0.2), inset 0 0 0 0 rgba(255, 255, 255, 0.1);
  }
  100% {
    box-shadow: 0 0 40px rgba(245, 158, 11, 0.6),
      0 0 80px rgba(37, 99, 235, 0.4), 0 0 120px rgba(5, 150, 105, 0.3),
      inset 0 0 20px 0 rgba(255, 255, 255, 0.2);
  }
}

@keyframes button-hover-wave {
  0% {
    transform: translateX(-100%) skewX(-12deg);
  }
  100% {
    transform: translateX(300%) skewX(-12deg);
  }
}

@keyframes button-text-jump {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-3px);
  }
}

@keyframes button-icon-spin {
  0% {
    transform: rotate(0deg) scale(1);
  }
  50% {
    transform: rotate(180deg) scale(1.2);
  }
  100% {
    transform: rotate(360deg) scale(1);
  }
}

@keyframes button-border-dance {
  0% {
    border-color: #fbbf24;
    border-width: 2px;
  }
  25% {
    border-color: #3b82f6;
    border-width: 3px;
  }
  50% {
    border-color: #10b981;
    border-width: 2px;
  }
  75% {
    border-color: #8b5cf6;
    border-width: 3px;
  }
  100% {
    border-color: #fbbf24;
    border-width: 2px;
  }
}

/* Button hover classes */
.button-epic-hover {
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.button-epic-hover:hover {
  animation: button-hover-scale 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards,
    button-hover-glow 0.4s ease-out forwards;
}

.button-epic-hover:hover .button-text {
  animation: button-text-jump 0.6s ease-in-out;
}

.button-epic-hover:hover .button-icon {
  animation: button-icon-spin 0.8s ease-in-out;
}

.button-epic-hover::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 70%
  );
  transform: translateX(-100%) skewX(-12deg);
  transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
}

.button-epic-hover:hover::before {
  animation: button-hover-wave 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.button-epic-hover::after {
  content: "";
  position: absolute;
  inset: -2px;
  border-radius: inherit;
  background: linear-gradient(
    45deg,
    #fbbf24,
    #3b82f6,
    #10b981,
    #8b5cf6,
    #fbbf24
  );
  background-size: 400% 400%;
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: -1;
}

.button-epic-hover:hover::after {
  opacity: 1;
  animation: button-border-dance 2s ease-in-out infinite;
}

.animate-button-pulse {
  animation: button-pulse 2s ease-in-out infinite;
}

.animate-fade-in-down {
  animation: fade-in-down 0.6s ease-out forwards;
  opacity: 0;
}

/* Performance-Optimized Animation Classes */
.animate-slide-in-left {
  animation: slide-in-left 0.8s ease-out forwards;
  opacity: 0;
}

.animate-slide-in-right {
  animation: slide-in-right 0.8s ease-out forwards;
  opacity: 0;
}

.animate-scale-in {
  animation: scale-in 0.5s ease-out forwards;
  opacity: 0;
}

.animate-bounce-in {
  animation: bounce-in 0.8s ease-out forwards;
  opacity: 0;
}

.animate-float-gentle {
  animation: float-gentle 4s ease-in-out infinite;
}

.animate-glow-pulse {
  animation: glow-pulse 3s ease-in-out infinite;
}

.animate-text-glow {
  animation: text-glow 2s ease-in-out infinite;
}

/* Staggered Animation Delays */
.delay-100 {
  animation-delay: 0.1s;
}
.delay-200 {
  animation-delay: 0.2s;
}
.delay-300 {
  animation-delay: 0.3s;
}
.delay-400 {
  animation-delay: 0.4s;
}
.delay-500 {
  animation-delay: 0.5s;
}
.delay-600 {
  animation-delay: 0.6s;
}
.delay-700 {
  animation-delay: 0.7s;
}
.delay-800 {
  animation-delay: 0.8s;
}

/* Performance Optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.gpu-accelerated {
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Fast Loading Optimizations */
* {
  box-sizing: border-box;
}

img {
  max-width: 100%;
  height: auto;
}

/* Reduce layout shifts */
.aspect-ratio-16-9 {
  aspect-ratio: 16 / 9;
}

.aspect-ratio-1-1 {
  aspect-ratio: 1 / 1;
}

/* Optimize repaints */
.contain-layout {
  contain: layout;
}

.contain-paint {
  contain: paint;
}

/* Critical rendering optimizations */
.critical-resource {
  font-display: swap;
}

/* Reduce animation on low-end devices */
@media (prefers-reduced-motion: reduce) {
  .animate-slide-in-left,
  .animate-slide-in-right,
  .animate-scale-in,
  .animate-bounce-in,
  .animate-float-gentle,
  .animate-glow-pulse,
  .animate-text-glow,
  .animate-scroll-to-left {
    animation: none;
    opacity: 1;
    transform: none;
  }
}

.shimmer {
  position: relative;
  overflow: hidden;
}

.shimmer::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: shimmer 2s infinite;
}

.dark .shimmer::before {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
}

.sponsor-banner {
  background: linear-gradient(
    135deg,
    rgba(245, 158, 11, 0.1) 0%,
    rgba(37, 99, 235, 0.1) 100%
  );
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.dark .sponsor-banner {
  background: linear-gradient(
    135deg,
    rgba(245, 158, 11, 0.2) 0%,
    rgba(37, 99, 235, 0.2) 100%
  );
  border: 1px solid rgba(245, 158, 11, 0.4);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

/* Enhanced Dark Mode Visibility for 131010 */
.dark {
  /* Ensure all text is ultra visible */
  --text-opacity: 1;
  color-scheme: dark;
}

.dark h1,
.dark h2,
.dark h3,
.dark h4,
.dark h5,
.dark h6 {
  color: hsl(var(--foreground));
  opacity: 1;
  /* text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5); */
}

.dark p,
.dark span,
.dark div,
.dark li,
.dark a {
  opacity: 1;
  color: inherit;
}

.dark .text-muted-foreground {
  color: hsl(var(--muted-foreground));
  opacity: 1;
}

.text-shadow-professional {
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dark .text-shadow-professional {
  text-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
}

/* Ultra-visible buttons in dark mode */
.dark .btn-primary {
  background: linear-gradient(135deg, #4f46e5 0%, #3b82f6 100%);
  box-shadow: 0 4px 20px rgba(79, 70, 229, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .btn-primary:hover {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  box-shadow: 0 6px 25px rgba(79, 70, 229, 0.7);
  transform: translateY(-1px);
}

/* Enhanced text visibility */
.dark .bolt-gradient-text {
  background: linear-gradient(
    135deg,
    #818cf8 0%,
    #60a5fa 25%,
    #34d399 50%,
    #fbbf24 75%,
    #f472b6 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 200% 200%;
  animation: gradient-shift 4s ease infinite;
}

/* Ultra-visible navigation */
.dark .nav-professional {
  background: rgba(19, 16, 16, 0.95);
  backdrop-filter: blur(32px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 1px 20px rgba(0, 0, 0, 0.8);
}

/* Enhanced responsive utilities */
@media (max-width: 640px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Reduce animation intensity on mobile */
  .floating-orb {
    animation-duration: 8s;
  }

  .shimmer::before {
    animation-duration: 3s;
  }
}


.typed-text {
  @apply text-2xl sm:text-4xl md:text-5xl lg:text-7xl leading-tight tracking-tight;
}
@keyframes slide-in {
  from {
    transform: translateX(-0.5ch);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.animate-slide-in {
  animation: slide-in 0.4s ease-out forwards;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .floating-orb,
  .animate-pulse,
  .animate-bounce,
  .animate-spin,
  .shimmer::before {
    animation: none;
  }

  .hover-lift:hover {
    transform: none;
  }
}

/* Print styles */
@media print {
  .floating-orb,
  .glass-effect,
  .mesh-gradient {
    display: none;
  }
}

/* Trailing Cursor Styles */
@layer components {
  /* Cursor animations and effects */
  .cursor-dot {
    position: fixed;
    top: 0;
    left: 0;
    pointer-events: none;
    z-index: 9999;
    border-radius: 50%;
    mix-blend-mode: difference;
    transition: transform 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: transform;
    backface-visibility: hidden;
  }

  .cursor-trail {
    position: fixed;
    top: 0;
    left: 0;
    pointer-events: none;
    z-index: 9998;
    border-radius: 50%;
    border: 2px solid;
    transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: transform;
    backface-visibility: hidden;
  }

  /* Cursor hover states for different elements */
  .cursor-hover-scale {
    transition: transform 0.2s ease-out;
  }

  .cursor-hover-scale:hover {
    transform: scale(1.05);
  }

  /* Enhanced focus styles for accessibility */
  *:focus-visible {
    outline: 2px solid rgb(59, 130, 246) !important;
    outline-offset: 2px !important;
    border-radius: 4px;
  }

  /* Enhanced cursor color variants with glow effects */
  .cursor-blue {
    --cursor-dot-color: rgb(59, 130, 246);
    --cursor-trail-color: rgba(59, 130, 246, 0.3);
    --cursor-glow: 0 0 20px rgba(59, 130, 246, 0.4);
  }

  .cursor-amber {
    --cursor-dot-color: rgb(245, 158, 11);
    --cursor-trail-color: rgba(245, 158, 11, 0.3);
    --cursor-glow: 0 0 20px rgba(245, 158, 11, 0.4);
  }

  .cursor-emerald {
    --cursor-dot-color: rgb(16, 185, 129);
    --cursor-trail-color: rgba(16, 185, 129, 0.3);
    --cursor-glow: 0 0 20px rgba(16, 185, 129, 0.4);
  }

  .cursor-purple {
    --cursor-dot-color: rgb(124, 58, 237);
    --cursor-trail-color: rgba(124, 58, 237, 0.3);
    --cursor-glow: 0 0 20px rgba(124, 58, 237, 0.4);
  }

  .cursor-rose {
    --cursor-dot-color: rgb(244, 63, 94);
    --cursor-trail-color: rgba(244, 63, 94, 0.3);
    --cursor-glow: 0 0 20px rgba(244, 63, 94, 0.4);
  }

  .cursor-cyan {
    --cursor-dot-color: rgb(6, 182, 212);
    --cursor-trail-color: rgba(6, 182, 212, 0.3);
    --cursor-glow: 0 0 20px rgba(6, 182, 212, 0.4);
  }

  /* Interactive element indicators */
  [data-interactive] {
    position: relative;
  }

  [data-interactive]::before {
    content: '';
    position: absolute;
    inset: -4px;
    border-radius: inherit;
    background: linear-gradient(45deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: -1;
  }

  [data-interactive]:hover::before {
    opacity: 1;
  }

  /* Smooth cursor transitions */
  .cursor-smooth {
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Performance optimizations for cursor */
  .cursor-optimized {
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
    perspective: 1000px;
  }
}

/* Cursor keyframe animations */
@keyframes cursor-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

@keyframes cursor-ripple {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes cursor-glow {
  0%, 100% {
    box-shadow: 0 0 10px currentColor;
  }
  50% {
    box-shadow: 0 0 20px currentColor, 0 0 30px currentColor;
  }
}

/* Cursor animation classes */
.animate-cursor-pulse {
  animation: cursor-pulse 1.5s ease-in-out infinite;
}

.animate-cursor-ripple {
  animation: cursor-ripple 0.6s ease-out;
}

.animate-cursor-glow {
  animation: cursor-glow 2s ease-in-out infinite;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .cursor-dot,
  .cursor-trail {
    transition: none;
    animation: none;
  }
  
  .animate-cursor-pulse,
  .animate-cursor-ripple,
  .animate-cursor-glow {
    animation: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .cursor-dot {
    mix-blend-mode: normal;
    background-color: #000 !important;
  }
  
  .dark .cursor-dot {
    background-color: #fff !important;
  }
  
  .cursor-trail {
    border-color: #000 !important;
    background-color: transparent !important;
  }
  
  .dark .cursor-trail {
    border-color: #fff !important;
  }
}

/* Mobile and touch device handling */
@media (hover: none) and (pointer: coarse) {
  .cursor-dot,
  .cursor-trail {
    display: none !important;
  }
}

/* Ensure cursor doesn't interfere with text selection */
::selection {
  background: rgba(59, 130, 246, 0.3);
}

::-moz-selection {
  background: rgba(59, 130, 246, 0.3);
}/* Enhanced
 Cursor Effects - Final Polish */
@layer components {
  /* Advanced cursor animations */
  @keyframes cursor-magnetic-pull {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
    }
    100% {
      transform: scale(1);
    }
  }

  @keyframes cursor-velocity-stretch {
    0% {
      transform: scaleX(1) scaleY(1);
    }
    100% {
      transform: scaleX(1.2) scaleY(0.8);
    }
  }

  @keyframes cursor-click-burst {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.5);
      opacity: 0.7;
    }
    100% {
      transform: scale(2);
      opacity: 0;
    }
  }

  @keyframes particle-float {
    0% {
      transform: translateY(0) scale(1);
      opacity: 0.8;
    }
    100% {
      transform: translateY(-30px) scale(0.5);
      opacity: 0;
    }
  }

  /* Enhanced interactive element effects */
  .cursor-magnetic {
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .cursor-magnetic:hover {
    animation: cursor-magnetic-pull 0.6s ease-in-out;
  }

  /* Velocity-based cursor effects */
  .cursor-velocity-active {
    animation: cursor-velocity-stretch 0.1s ease-out;
  }

  /* Click burst effect */
  .cursor-click-burst {
    animation: cursor-click-burst 0.4s ease-out;
  }

  /* Particle effects */
  .cursor-particle {
    animation: particle-float 1s ease-out forwards;
  }

  /* Enhanced glow effects for different cursor states */
  .cursor-glow-active {
    filter: drop-shadow(0 0 8px currentColor) drop-shadow(0 0 16px currentColor);
  }

  .cursor-glow-hover {
    filter: drop-shadow(0 0 12px currentColor) drop-shadow(0 0 24px currentColor) drop-shadow(0 0 36px currentColor);
  }

  /* Smooth transitions for all cursor elements */
  .cursor-smooth-transition {
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Enhanced focus styles for better accessibility */
  .cursor-focus-enhanced:focus-visible {
    outline: 3px solid currentColor !important;
    outline-offset: 3px !important;
    box-shadow: 0 0 0 6px rgba(59, 130, 246, 0.2) !important;
    border-radius: 8px !important;
  }

  /* Performance optimizations for cursor elements */
  .cursor-optimized {
    will-change: transform, opacity;
    backface-visibility: hidden;
    perspective: 1000px;
    transform-style: preserve-3d;
  }

  /* Enhanced interactive states */
  .cursor-interactive-enhanced {
    position: relative;
    overflow: hidden;
  }

  .cursor-interactive-enhanced::before {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(45deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: -1;
  }

  .cursor-interactive-enhanced:hover::before {
    opacity: 1;
    animation: shimmer 1.5s ease-in-out infinite;
  }

  /* Dynamic cursor trail effects */
  .cursor-trail-dynamic {
    background: radial-gradient(
      circle,
      currentColor 0%,
      transparent 70%
    );
    backdrop-filter: blur(1px);
  }

  /* Magnetic field effect for interactive elements */
  .cursor-magnetic-field {
    position: relative;
  }

  .cursor-magnetic-field::after {
    content: '';
    position: absolute;
    inset: -10px;
    border-radius: 50%;
    background: radial-gradient(
      circle,
      transparent 60%,
      rgba(59, 130, 246, 0.05) 70%,
      transparent 100%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: -1;
  }

  .cursor-magnetic-field:hover::after {
    opacity: 1;
    animation: cursor-magnetic-pull 2s ease-in-out infinite;
  }
}

/* Responsive cursor enhancements */
@media (min-width: 768px) {
  .cursor-enhanced-desktop {
    cursor: none !important;
  }
  
  .cursor-enhanced-desktop * {
    cursor: none !important;
  }
}

/* High refresh rate optimizations */
@media (min-resolution: 120dpi) {
  .cursor-optimized {
    transform: translateZ(0);
  }
}

/* Reduced motion accessibility */
@media (prefers-reduced-motion: reduce) {
  .cursor-magnetic:hover,
  .cursor-velocity-active,
  .cursor-click-burst,
  .cursor-particle {
    animation: none !important;
  }
  
  .cursor-interactive-enhanced:hover::before {
    animation: none !important;
  }
}

/* Dark mode cursor enhancements */
.dark .cursor-glow-active {
  filter: drop-shadow(0 0 10px currentColor) drop-shadow(0 0 20px currentColor);
}

.dark .cursor-glow-hover {
  filter: drop-shadow(0 0 15px currentColor) drop-shadow(0 0 30px currentColor) drop-shadow(0 0 45px currentColor);
}

/* Ultra-smooth cursor for high-end devices */
@supports (backdrop-filter: blur(10px)) {
  .cursor-trail-premium {
    backdrop-filter: blur(2px) saturate(1.2);
    background: radial-gradient(
      circle,
      rgba(59, 130, 246, 0.4) 0%,
      rgba(59, 130, 246, 0.2) 50%,
      transparent 100%
    );
  }
}

/* Advanced Cursor Effects - Final Enhancements */
@layer components {
  /* Smooth slider styles for cursor settings */
  input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
    background: transparent;
    cursor: pointer;
  }

  input[type="range"]::-webkit-slider-track {
    background: hsl(var(--muted));
    height: 8px;
    border-radius: 4px;
  }

  input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    height: 20px;
    width: 20px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3);
    transition: all 0.2s ease;
  }

  input[type="range"]::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
  }

  input[type="range"]::-moz-range-track {
    background: hsl(var(--muted));
    height: 8px;
    border-radius: 4px;
    border: none;
  }

  input[type="range"]::-moz-range-thumb {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    height: 20px;
    width: 20px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3);
    cursor: pointer;
  }

  /* Enhanced toggle switch styles */
  .cursor-toggle {
    position: relative;
    display: inline-block;
    width: 48px;
    height: 24px;
  }

  .cursor-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  .cursor-toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: hsl(var(--muted));
    transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 24px;
  }

  .cursor-toggle-slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .cursor-toggle input:checked + .cursor-toggle-slider {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
  }

  .cursor-toggle input:checked + .cursor-toggle-slider:before {
    transform: translateX(24px);
    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3);
  }

  /* Advanced cursor trail effects */
  .cursor-trail-premium {
    background: radial-gradient(
      circle,
      currentColor 0%,
      rgba(59, 130, 246, 0.4) 30%,
      rgba(59, 130, 246, 0.2) 60%,
      transparent 100%
    );
    backdrop-filter: blur(3px) saturate(1.5);
    border: 1px solid rgba(59, 130, 246, 0.2);
  }

  /* Magnetic field visualization */
  .cursor-magnetic-field-active {
    position: relative;
  }

  .cursor-magnetic-field-active::before {
    content: '';
    position: absolute;
    inset: -20px;
    border-radius: 50%;
    background: radial-gradient(
      circle,
      transparent 40%,
      rgba(59, 130, 246, 0.1) 50%,
      rgba(59, 130, 246, 0.05) 70%,
      transparent 100%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: -1;
    animation: magnetic-pulse 2s ease-in-out infinite;
  }

  .cursor-magnetic-field-active:hover::before {
    opacity: 1;
  }

  /* Rainbow cursor animations */
  @keyframes rainbow-shift {
    0% { filter: hue-rotate(0deg); }
    25% { filter: hue-rotate(90deg); }
    50% { filter: hue-rotate(180deg); }
    75% { filter: hue-rotate(270deg); }
    100% { filter: hue-rotate(360deg); }
  }

  .cursor-rainbow-active {
    animation: rainbow-shift 3s linear infinite;
  }

  /* Velocity-based stretch effect */
  @keyframes velocity-stretch-x {
    0% { transform: scaleX(1) scaleY(1); }
    100% { transform: scaleX(1.5) scaleY(0.8); }
  }

  @keyframes velocity-stretch-y {
    0% { transform: scaleX(1) scaleY(1); }
    100% { transform: scaleX(0.8) scaleY(1.5); }
  }

  .cursor-velocity-x {
    animation: velocity-stretch-x 0.1s ease-out;
  }

  .cursor-velocity-y {
    animation: velocity-stretch-y 0.1s ease-out;
  }

  /* Enhanced glow effects */
  .cursor-glow-intense {
    filter: 
      drop-shadow(0 0 4px currentColor) 
      drop-shadow(0 0 8px currentColor) 
      drop-shadow(0 0 12px currentColor)
      drop-shadow(0 0 16px currentColor);
  }

  .cursor-glow-soft {
    filter: 
      drop-shadow(0 0 6px currentColor) 
      drop-shadow(0 0 12px currentColor);
  }

  /* Particle trail enhancements */
  @keyframes particle-sparkle {
    0% {
      transform: scale(0) rotate(0deg);
      opacity: 1;
    }
    50% {
      transform: scale(1) rotate(180deg);
      opacity: 0.8;
    }
    100% {
      transform: scale(0) rotate(360deg);
      opacity: 0;
    }
  }

  .cursor-particle-sparkle {
    animation: particle-sparkle 0.8s ease-out forwards;
  }

  /* Click burst enhancements */
  @keyframes click-burst-enhanced {
    0% {
      transform: scale(0);
      opacity: 1;
    }
    50% {
      transform: scale(1.2);
      opacity: 0.6;
    }
    100% {
      transform: scale(2.5);
      opacity: 0;
    }
  }

  .cursor-click-burst-enhanced {
    animation: click-burst-enhanced 0.4s ease-out forwards;
  }

  /* Smooth fade animations */
  @keyframes fadeOut {
    0% { opacity: 1; }
    100% { opacity: 0; }
  }

  @keyframes fadeInScale {
    0% { 
      opacity: 0; 
      transform: scale(0.8);
    }
    100% { 
      opacity: 1; 
      transform: scale(1);
    }
  }

  .cursor-fade-out {
    animation: fadeOut 0.3s ease-out forwards;
  }

  .cursor-fade-in-scale {
    animation: fadeInScale 0.3s ease-out forwards;
  }

  /* Enhanced magnetic hover effects */
  .cursor-magnetic-hover {
    transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .cursor-magnetic-hover:hover {
    transform: scale(1.05) translateZ(0);
  }

  /* Settings panel enhancements */
  .cursor-settings-panel {
    backdrop-filter: blur(20px) saturate(1.2);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 
      0 8px 32px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  .dark .cursor-settings-panel {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 
      0 8px 32px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  /* Tab navigation enhancements */
  .cursor-tab-active {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.05));
    border: 1px solid rgba(59, 130, 246, 0.2);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
  }

  .cursor-tab-inactive {
    background: transparent;
    border: 1px solid transparent;
    transition: all 0.2s ease;
  }

  .cursor-tab-inactive:hover {
    background: rgba(59, 130, 246, 0.05);
    border: 1px solid rgba(59, 130, 246, 0.1);
  }

  /* Performance optimizations */
  .cursor-optimized-gpu {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
    will-change: transform, opacity;
  }

  /* Accessibility enhancements */
  .cursor-accessible-focus {
    outline: 2px solid rgb(59, 130, 246);
    outline-offset: 2px;
    border-radius: 4px;
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .cursor-dot {
      mix-blend-mode: normal;
      background-color: #000 !important;
      border: 2px solid #fff;
    }
    
    .dark .cursor-dot {
      background-color: #fff !important;
      border: 2px solid #000;
    }
    
    .cursor-trail {
      border: 3px solid #000 !important;
      background-color: rgba(0, 0, 0, 0.3) !important;
    }
    
    .dark .cursor-trail {
      border: 3px solid #fff !important;
      background-color: rgba(255, 255, 255, 0.3) !important;
    }
  }

  /* Reduced motion accessibility */
  @media (prefers-reduced-motion: reduce) {
    .cursor-rainbow-active,
    .cursor-velocity-x,
    .cursor-velocity-y,
    .cursor-particle-sparkle,
    .cursor-click-burst-enhanced {
      animation: none !important;
    }
    
    .cursor-magnetic-hover:hover {
      transform: none !important;
    }
    
    .cursor-trail-premium {
      backdrop-filter: none !important;
    }
  }
}

/* Ultra-smooth scrolling for settings panel */
.cursor-settings-scroll {
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.3) transparent;
}

.cursor-settings-scroll::-webkit-scrollbar {
  width: 6px;
}

.cursor-settings-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.cursor-settings-scroll::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.3);
  border-radius: 3px;
}

.cursor-settings-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}