
# APP CONFIGURATION
NEXT_PUBLIC_APP_NAME=DocMagic
NEXT_PUBLIC_APP_URL=http://localhost:3000

# SECURITY CONFIGURATION
# Generate a strong random secret for session encryption
NEXTAUTH_SECRET=your-nextauth-secret-here-use-openssl-rand-base64-32

# Stripe Configuration (required for real payments)
# Get these from your Stripe Dashboard
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
STRIPE_SECRET_KEY=sk_test_your_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
STRIPE_PRICE_ID=price_your_price_id_here

# Set to 'true' to enable Stripe integration
NEXT_PUBLIC_ENABLE_STRIPE=false

# SUPABASE CONFIGURATION
# Get these from your Supabase project settings
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key-here

# GEMINI AI CONFIGURATION
# Get this from Google AI Studio
GEMINI_API_KEY=your-gemini-api-key-here

# PEXELS API (Optional - for stock images)
PEXELS_API_KEY=your-pexels-api-key-here

# EMAIL CONFIGURATION (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# DEPLOYMENT CONFIGURATION
NEXT_PUBLIC_VERCEL_URL=https://your-vercel-project-url.vercel.app
NEXT_PUBLIC_NETLIFY_URL=https://your-netlify-project-url.netlify.app

# ENVIRONMENT
NODE_ENV=development
