# ❓ Frequently Asked Questions

## 🤔 General

### What is DocMagic?
DocMagic is an open-source, AI-powered document creation platform that helps users generate professional documents like resumes, presentations, CVs, and letters using natural language descriptions.

### Is DocMagic really free?
Yes! DocMagic is 100% open source and free to use. You can self-host it or use our hosted version. Some advanced features may require API keys for services like Gemini AI.

### How do I get started?
1. Visit our [live demo](https://docmagic1.netlify.app)
2. Sign up for a free account
3. Choose a document type
4. Describe what you need in natural language
5. Let the AI work its magic!

## 🛠 Technical

### What technologies power DocMagic?
DocMagic is built with:
- Frontend: Next.js 15, React 18, TypeScript, Tailwind CSS
- Backend: Supabase, Node.js
- AI: Google's Gemini
- Styling: Framer Motion, Shadcn UI
- Hosting: Vercel/Netlify

### How do I self-host DocMagic?
See our [Self-Hosting Guide](https://github.com/docmagic-ai/docmagic#-self-hosting) in the README for detailed instructions on setting up your own instance.

### What are the system requirements?
- Node.js 18+
- npm or yarn
- Modern web browser (Chrome, Firefox, Safari, Edge)
- Supabase account (for database)
- Google Cloud account (for Gemini AI)

## 🔒 Security & Privacy

### Is my data secure?
Yes! DocMagic runs entirely in your browser. Your documents are never stored on our servers unless you choose to save them to your account.

### What happens to my documents?
Documents are processed in your browser and can be downloaded directly to your device. If you create an account, you can choose to save documents to your private library.

### Do you store my API keys?
No. API keys are stored locally in your browser and are never sent to our servers.

## 💡 Tips & Best Practices

### How can I get the best results?
- Be specific in your descriptions
- Include key details like job title, years of experience, and skills
- Use the preview feature to make adjustments
- Try different variations of your prompt

### What document formats are supported?
Currently supported formats:
- PDF (all document types)
- DOCX (Word documents)
- PPTX (PowerPoint presentations)

### Can I customize the templates?
Yes! You can fork the repository and modify the template files to match your preferred style.

## 🤝 Community & Support

### How can I contribute?
We love contributions! Check out our [Contributing Guidelines](https://github.com/docmagic-ai/docmagic/blob/main/CONTRIBUTING.md) to get started.

### Where can I report bugs or request features?
Please open an issue on our [GitHub repository](https://github.com/docmagic-ai/docmagic/issues).

### Is there a community or forum?
Yes! Join our [Discord community](https://discord.gg/docmagic) to connect with other users and developers.

## 🔄 Troubleshooting

### The AI isn't generating what I want
Try these tips:
1. Be more specific in your description
2. Break complex requests into simpler ones
3. Check if you've provided enough context
4. Try different phrasing

### I'm getting an error when generating documents
1. Check your internet connection
2. Make sure you're using a supported browser
3. Clear your browser cache and refresh
4. If the problem persists, please [report the issue](https://github.com/docmagic-ai/docmagic/issues)

### The document formatting looks off
1. Try downloading in a different format
2. Check if you have the latest version of the software
3. Some complex formatting might need manual adjustment after generation

## 📈 Enterprise & Business

### Can I use DocMagic for my business?
Absolutely! DocMagic is open source under the MIT license, so you can use it for personal or commercial projects.

### Do you offer enterprise support?
While we don't offer official enterprise support, our community is very active and helpful. For custom development needs, you can hire from our community of contributors.

### Can I integrate DocMagic with my existing tools?
Yes! DocMagic provides an API that you can use to integrate with other tools in your workflow. Check out our [API documentation](./API.md) for more details.

---

Still have questions? Feel free to [open an issue](https://github.com/docmagic-ai/docmagic/issues) or [join our community](https://discord.gg/docmagic) for help!
