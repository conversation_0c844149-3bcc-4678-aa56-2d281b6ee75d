'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Eye, 
  Download, 
  Share2, 
  Star, 
  Users, 
  Calendar,
  Tag,
  Briefcase,
  FileText,
  Presentation,
  Mail,
  GraduationCap,
  X
} from 'lucide-react';
import { Template } from '@/types/templates';
import { getTemplateTypeIcon } from '@/lib/templates';
import { useRouter } from 'next/navigation';

interface TemplatePreviewModalProps {
  template: Template | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onUseTemplate?: (template: Template) => void;
}

export function TemplatePreviewModal({
  template,
  open,
  onOpenChange,
  onUseTemplate
}: TemplatePreviewModalProps) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('preview');

  if (!template) return null;

  const handleUseTemplate = () => {
    if (onUseTemplate) {
      onUseTemplate(template);
    } else {
      router.push(`/templates/${template.id}/use`);
    }
    onOpenChange(false);
  };

  const renderTemplateContent = () => {
    if (!template.content || Object.keys(template.content).length === 0) {
      return (
        <div className="text-center py-12">
          <div className="text-muted-foreground mb-4">
            No preview content available for this template.
          </div>
          <div className="text-sm text-muted-foreground">
            This template will provide structure and formatting when you use it.
          </div>
        </div>
      );
    }

    try {
      switch (template.type) {
        case 'resume':
          return renderResumePreview();
        case 'presentation':
          return renderPresentationPreview();
        case 'letter':
          return renderLetterPreview();
        case 'cv':
          return renderCVPreview();
        default:
          return <div className="text-muted-foreground">Preview not available for this template type.</div>;
      }
    } catch (error) {
      console.error('Error rendering template preview:', error);
      return (
        <div className="text-center py-12">
          <div className="text-muted-foreground mb-4">
            Unable to render preview for this template.
          </div>
          <div className="text-sm text-muted-foreground">
            Template: {template.title} ({template.type})
          </div>
        </div>
      );
    }
  };

  const renderResumePreview = () => {
    const content = template.content as any;

    // Handle missing or empty content
    const personalInfo = content.personalInfo || {};
    const sections = content.sections || [];

    // Create sample content if none exists
    if (sections.length === 0) {
      return (
        <div className="space-y-6 p-6 bg-white border rounded-lg">
          <div className="text-center border-b pb-4">
            <h1 className="text-2xl font-bold text-gray-900">
              {template.title}
            </h1>
            <div className="text-sm text-gray-600 mt-2">
              Professional Resume Template
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-2">Template Features</h2>
              <ul className="text-sm text-gray-700 space-y-2">
                <li className="flex items-start">
                  <span className="mr-2">•</span>
                  <span>Professional formatting and layout</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2">•</span>
                  <span>Customizable sections for experience, education, and skills</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2">•</span>
                  <span>Industry-standard structure and design</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2">•</span>
                  <span>ATS-friendly formatting</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-6 p-6 bg-white border rounded-lg">
        {/* Header */}
        <div className="text-center border-b pb-4">
          <h1 className="text-2xl font-bold text-gray-900">
            {personalInfo.name || '[Your Name]'}
          </h1>
          <div className="text-sm text-gray-600 mt-2 space-x-2">
            <span>{personalInfo.email || '[<EMAIL>]'}</span>
            <span>•</span>
            <span>{personalInfo.phone || '[phone]'}</span>
            <span>•</span>
            <span>{personalInfo.location || '[location]'}</span>
          </div>
          {personalInfo.website && (
            <div className="text-sm text-blue-600 mt-1">
              {personalInfo.website}
            </div>
          )}
        </div>

        {/* Professional Summary */}
        {personalInfo.summary && (
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-2">Professional Summary</h2>
            <p className="text-gray-700 text-sm leading-relaxed">
              {personalInfo.summary}
            </p>
          </div>
        )}

        {/* Sections */}
        {sections.map((section: any, index: number) => (
          <div key={section.id || index}>
            <h2 className="text-lg font-semibold text-gray-900 mb-3">{section.title}</h2>
            {section.items?.slice(0, 2).map((item: any, itemIndex: number) => (
              <div key={itemIndex} className="mb-4 last:mb-0">
                {item.position && (
                  <div className="flex justify-between items-start mb-1">
                    <h3 className="font-medium text-gray-900">{item.position}</h3>
                    <span className="text-sm text-gray-600">{item.duration}</span>
                  </div>
                )}
                {item.company && (
                  <div className="text-sm text-gray-700 mb-2">
                    {item.company} • {item.location}
                  </div>
                )}
                {item.achievements && (
                  <ul className="text-sm text-gray-700 space-y-1">
                    {item.achievements.slice(0, 3).map((achievement: string, achIndex: number) => (
                      <li key={achIndex} className="flex items-start">
                        <span className="mr-2">•</span>
                        <span>{achievement}</span>
                      </li>
                    ))}
                  </ul>
                )}
                {item.skills && (
                  <div className="text-sm text-gray-700">
                    <strong>{item.category}:</strong> {item.skills}
                  </div>
                )}
              </div>
            ))}
            {section.items?.length > 2 && (
              <div className="text-sm text-gray-500 italic">
                ... and {section.items.length - 2} more items
              </div>
            )}
          </div>
        ))}
      </div>
    );
  };

  const renderPresentationPreview = () => {
    const content = template.content as any;

    // Handle case where slides might not exist or be structured differently
    const slides = content.slides || [];
    const title = content.title || template.title || 'Presentation Template';

    if (slides.length === 0) {
      // Create sample slides for preview
      const sampleSlides = [
        {
          id: '1',
          type: 'title',
          content: {
            title: title,
            subtitle: 'Professional presentation template'
          }
        },
        {
          id: '2',
          type: 'content',
          content: {
            title: 'Overview',
            bullets: ['Key point 1', 'Key point 2', 'Key point 3']
          }
        }
      ];

      return (
        <div className="space-y-4">
          <div className="text-center mb-6">
            <h1 className="text-xl font-bold text-gray-900 mb-2">{title}</h1>
            <p className="text-sm text-gray-600">Sample presentation structure</p>
          </div>

          <div className="grid grid-cols-2 gap-4">
            {sampleSlides.map((slide: any, index: number) => (
              <div key={index} className="border rounded-lg p-4 bg-white aspect-video flex flex-col justify-center min-h-[200px]">
                <div className="text-xs text-gray-500 mb-2">Slide {index + 1}</div>
                <h3 className="font-medium text-sm mb-2 text-gray-900">{slide.content?.title || `Slide ${index + 1}`}</h3>
                {slide.content?.subtitle && (
                  <p className="text-xs text-gray-600 mb-2">{slide.content.subtitle}</p>
                )}
                {slide.content?.bullets && (
                  <ul className="text-xs text-gray-700 space-y-1">
                    {slide.content.bullets.slice(0, 3).map((bullet: string, bulletIndex: number) => (
                      <li key={bulletIndex} className="flex items-start">
                        <span className="mr-1">•</span>
                        <span>{bullet}</span>
                      </li>
                    ))}
                  </ul>
                )}
              </div>
            ))}
          </div>

          <div className="text-center text-sm text-gray-500">
            This template provides structure for creating professional presentations
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        <div className="text-center mb-6">
          <h1 className="text-xl font-bold text-gray-900 mb-2">{title}</h1>
          <p className="text-sm text-gray-600">{slides.length} slides</p>
        </div>

        <div className="grid grid-cols-2 gap-4">
          {slides.slice(0, 4).map((slide: any, index: number) => (
            <div key={index} className="border rounded-lg p-4 bg-white aspect-video flex flex-col justify-center min-h-[200px]">
              <div className="text-xs text-gray-500 mb-2">Slide {index + 1}</div>
              <h3 className="font-medium text-sm mb-2 text-gray-900">{slide.content?.title || slide.title || `Slide ${index + 1}`}</h3>
              {(slide.content?.subtitle || slide.subtitle) && (
                <p className="text-xs text-gray-600 mb-2">{slide.content?.subtitle || slide.subtitle}</p>
              )}
              {(slide.content?.bullets || slide.bullets) && (
                <ul className="text-xs text-gray-700 space-y-1">
                  {(slide.content?.bullets || slide.bullets).slice(0, 3).map((bullet: string, bulletIndex: number) => (
                    <li key={bulletIndex} className="flex items-start">
                      <span className="mr-1">•</span>
                      <span>{bullet}</span>
                    </li>
                  ))}
                </ul>
              )}
              {/* Show any other content if bullets/subtitle don't exist */}
              {!slide.content?.bullets && !slide.bullets && !slide.content?.subtitle && !slide.subtitle && (
                <div className="text-xs text-gray-600">
                  {slide.content?.description || slide.description || 'Slide content'}
                </div>
              )}
            </div>
          ))}
        </div>

        {slides.length > 4 && (
          <div className="text-center text-sm text-gray-500">
            ... and {slides.length - 4} more slides
          </div>
        )}
      </div>
    );
  };

  const renderLetterPreview = () => {
    const content = template.content as any;

    // Handle missing content
    if (!content.sender && !content.recipient && !content.content) {
      return (
        <div className="bg-white p-8 border rounded-lg max-w-2xl mx-auto">
          <div className="text-center">
            <h2 className="text-xl font-bold text-gray-900 mb-4">{template.title}</h2>
            <div className="space-y-4 text-sm text-gray-600">
              <p>This professional letter template includes:</p>
              <ul className="text-left space-y-2">
                <li className="flex items-start">
                  <span className="mr-2">•</span>
                  <span>Professional header with sender information</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2">•</span>
                  <span>Proper date and recipient formatting</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2">•</span>
                  <span>Structured body paragraphs</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2">•</span>
                  <span>Professional closing and signature</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="bg-white p-8 border rounded-lg max-w-2xl mx-auto">
        {/* Sender Info */}
        {content.sender && (
          <div className="mb-6">
            <div className="font-medium">{content.sender.name || '[Your Name]'}</div>
            <div className="text-sm text-gray-600">
              {content.sender.address && <div>{content.sender.address}</div>}
              {content.sender.city_state_zip && <div>{content.sender.city_state_zip}</div>}
              {content.sender.phone && <div>{content.sender.phone}</div>}
              {content.sender.email && <div>{content.sender.email}</div>}
            </div>
          </div>
        )}

        {/* Date */}
        <div className="mb-6 text-sm">
          {content.sender?.date || new Date().toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          })}
        </div>

        {/* Recipient */}
        {content.recipient && (
          <div className="mb-6">
            <div className="font-medium">{content.recipient.name || '[Recipient Name]'}</div>
            <div className="text-sm text-gray-600">
              {content.recipient.title && <div>{content.recipient.title}</div>}
              {content.recipient.company && <div>{content.recipient.company}</div>}
              {content.recipient.address && <div>{content.recipient.address}</div>}
              {content.recipient.city_state_zip && <div>{content.recipient.city_state_zip}</div>}
            </div>
          </div>
        )}

        {/* Letter Content */}
        <div className="space-y-4 text-sm">
          {content.content?.subject && (
            <div>
              <strong>Subject: {content.content.subject}</strong>
            </div>
          )}

          <div>{content.content?.greeting || 'Dear Hiring Manager,'}</div>

          {content.content?.opening_paragraph && (
            <p className="leading-relaxed">{content.content.opening_paragraph}</p>
          )}

          {content.content?.body_paragraph_1 && (
            <p className="leading-relaxed">{content.content.body_paragraph_1}</p>
          )}

          {content.content?.body && (
            <p className="leading-relaxed">{content.content.body}</p>
          )}

          <div className="mt-6">
            <div>{content.content?.closing || 'Sincerely,'}</div>
            <div className="mt-4 font-medium">{content.content?.signature || '[Your Name]'}</div>
          </div>
        </div>
      </div>
    );
  };

  const renderCVPreview = () => {
    const content = template.content as any;

    // Handle missing content
    const personalInfo = content.personalInfo || {};
    const sections = content.sections || [];

    if (sections.length === 0) {
      return (
        <div className="space-y-6 p-6 bg-white border rounded-lg">
          <div className="text-center border-b pb-4">
            <h1 className="text-2xl font-bold text-gray-900">
              {template.title}
            </h1>
            <div className="text-sm text-gray-600 mt-2">
              Academic CV Template
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-2">Template Features</h2>
              <ul className="text-sm text-gray-700 space-y-2">
                <li className="flex items-start">
                  <span className="mr-2">•</span>
                  <span>Comprehensive academic formatting</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2">•</span>
                  <span>Sections for education, research, and publications</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2">•</span>
                  <span>Professional academic layout</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2">•</span>
                  <span>Customizable sections for different academic fields</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-6 p-6 bg-white border rounded-lg">
        {/* Header */}
        <div className="text-center border-b pb-4">
          <h1 className="text-2xl font-bold text-gray-900">
            {personalInfo.name || '[Your Name]'}
          </h1>
          {personalInfo.title && (
            <div className="text-lg text-gray-700 mt-1">{personalInfo.title}</div>
          )}
          <div className="text-sm text-gray-600 mt-2 space-x-2">
            <span>{personalInfo.email || '[<EMAIL>]'}</span>
            <span>•</span>
            <span>{personalInfo.phone || '[phone]'}</span>
          </div>
          {personalInfo.orcid && (
            <div className="text-sm text-blue-600 mt-1">
              ORCID: {personalInfo.orcid}
            </div>
          )}
        </div>

        {/* Summary */}
        {personalInfo.summary && (
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-2">Professional Summary</h2>
            <p className="text-gray-700 text-sm leading-relaxed">
              {personalInfo.summary}
            </p>
          </div>
        )}

        {/* Sections Preview */}
        {sections.slice(0, 3).map((section: any, index: number) => (
          <div key={section.id || index}>
            <h2 className="text-lg font-semibold text-gray-900 mb-3">{section.title}</h2>
            {section.items?.slice(0, 2).map((item: any, itemIndex: number) => (
              <div key={itemIndex} className="mb-3 text-sm">
                {item.degree && (
                  <div className="font-medium">{item.degree}</div>
                )}
                {item.position && (
                  <div className="font-medium">{item.position}</div>
                )}
                {item.institution && (
                  <div className="text-gray-700">{item.institution}</div>
                )}
                {item.year && (
                  <div className="text-gray-600">{item.year}</div>
                )}
              </div>
            ))}
          </div>
        ))}
      </div>
    );
  };

  const templateMetadata = template as any;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader className="pb-4">
          <div className="flex items-center space-x-3">
            <span className="text-2xl">{getTemplateTypeIcon(template.type)}</span>
            <div>
              <DialogTitle className="text-xl">{template.title}</DialogTitle>
              <p className="text-sm text-muted-foreground mt-1">
                {template.description}
              </p>
            </div>
          </div>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="preview">
              <Eye className="h-4 w-4 mr-2" />
              Preview
            </TabsTrigger>
            <TabsTrigger value="details">
              <FileText className="h-4 w-4 mr-2" />
              Details
            </TabsTrigger>
            <TabsTrigger value="metadata">
              <Tag className="h-4 w-4 mr-2" />
              Info
            </TabsTrigger>
          </TabsList>

          <TabsContent value="preview" className="flex-1 mt-4">
            <ScrollArea className="h-[60vh]">
              {renderTemplateContent()}
            </ScrollArea>
          </TabsContent>

          <TabsContent value="details" className="flex-1 mt-4">
            <ScrollArea className="h-[60vh]">
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold mb-2">Template Structure</h3>
                  <div className="text-sm text-muted-foreground">
                    This template includes the following sections and features:
                  </div>
                </div>
                
                {template.content && typeof template.content === 'object' && (
                  <div className="space-y-3">
                    {Object.keys(template.content).map((key) => (
                      <div key={key} className="border rounded-lg p-3">
                        <div className="font-medium capitalize">{key.replace(/([A-Z])/g, ' $1')}</div>
                        <div className="text-sm text-muted-foreground mt-1">
                          {typeof (template.content as any)[key] === 'object' 
                            ? `Contains ${Object.keys((template.content as any)[key]).length} items`
                            : 'Configuration included'
                          }
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="metadata" className="flex-1 mt-4">
            <ScrollArea className="h-[60vh]">
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm font-medium">Type</div>
                    <Badge variant="outline" className="mt-1">
                      {template.type.charAt(0).toUpperCase() + template.type.slice(1)}
                    </Badge>
                  </div>
                  
                  {templateMetadata.industry && (
                    <div>
                      <div className="text-sm font-medium">Industry</div>
                      <div className="text-sm text-muted-foreground mt-1 capitalize">
                        {templateMetadata.industry}
                      </div>
                    </div>
                  )}
                  
                  {templateMetadata.difficulty_level && (
                    <div>
                      <div className="text-sm font-medium">Difficulty</div>
                      <Badge 
                        variant={templateMetadata.difficulty_level === 'beginner' ? 'default' : 
                               templateMetadata.difficulty_level === 'intermediate' ? 'secondary' : 'destructive'}
                        className="mt-1"
                      >
                        {templateMetadata.difficulty_level}
                      </Badge>
                    </div>
                  )}
                  
                  {templateMetadata.usage_count && (
                    <div>
                      <div className="text-sm font-medium">Usage Count</div>
                      <div className="text-sm text-muted-foreground mt-1 flex items-center">
                        <Users className="h-3 w-3 mr-1" />
                        {templateMetadata.usage_count.toLocaleString()}
                      </div>
                    </div>
                  )}
                  
                  {templateMetadata.rating && (
                    <div>
                      <div className="text-sm font-medium">Rating</div>
                      <div className="text-sm text-muted-foreground mt-1 flex items-center">
                        <Star className="h-3 w-3 mr-1 fill-yellow-400 text-yellow-400" />
                        {templateMetadata.rating}/5.0
                      </div>
                    </div>
                  )}
                  
                  <div>
                    <div className="text-sm font-medium">Last Updated</div>
                    <div className="text-sm text-muted-foreground mt-1 flex items-center">
                      <Calendar className="h-3 w-3 mr-1" />
                      {new Date(template.updated_at).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                      })}
                    </div>
                  </div>
                  
                  <div>
                    <div className="text-sm font-medium">Visibility</div>
                    <Badge variant={template.is_public ? 'default' : 'secondary'} className="mt-1">
                      {template.is_public ? 'Public' : 'Private'}
                    </Badge>
                  </div>
                </div>
                
                {templateMetadata.tags && templateMetadata.tags.length > 0 && (
                  <div>
                    <div className="text-sm font-medium mb-2">Tags</div>
                    <div className="flex flex-wrap gap-2">
                      {templateMetadata.tags.map((tag: string, index: number) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </ScrollArea>
          </TabsContent>
        </Tabs>

        <Separator />
        
        <div className="flex justify-between items-center pt-4">
          <div className="flex space-x-2">
            <Button variant="outline" size="sm">
              <Share2 className="h-4 w-4 mr-2" />
              Share
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Download
            </Button>
          </div>
          
          <div className="flex space-x-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Close
            </Button>
            <Button onClick={handleUseTemplate}>
              Use This Template
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
